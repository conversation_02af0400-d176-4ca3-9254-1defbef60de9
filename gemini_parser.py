import os
import json
import requests
import re
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
import backoff
from typing import Dict, List, Optional, Tuple

class GeminiParser:
    def __init__(self, api_key: str):
        """Initialize the Gemini parser with API key"""
        self.api_key = api_key
        self.location_info = None  # Will store location-specific data from CSV

    def set_location_info(self, location_data: Dict):
        """Set location-specific information from CSV to use in prompts

        Args:
            location_data: Dictionary containing location info (name, address, etc.)
        """
        self.location_info = location_data

    @backoff.on_exception(
        backoff.expo,
        (requests.exceptions.RequestException, json.JSONDecodeError),
        max_tries=3,
        max_time=30,
        on_backoff=lambda details: print(f"Retrying Gemini API call (attempt {details['tries']} after {details['wait']}s wait)")
    )
    def _preprocess_content(self, content: str) -> str:
        """
        Preprocess crawled content to reduce token count by precisely removing navigation elements
        while preserving valuable content and page structure.

        Args:
            content: Raw crawled content with multiple pages

        Returns:
            Preprocessed content with reduced token count
        """
        # Remove header comments (Website Crawl Results, Starting URL, Date)
        content = re.sub(r'# Website Crawl Results.*?\n', '', content, flags=re.DOTALL)
        content = re.sub(r'# Starting URL:.*?\n', '', content, flags=re.DOTALL)
        content = re.sub(r'# Date:.*?\n', '', content, flags=re.DOTALL)
        content = re.sub(r'# Website Crawl Results \(Jina Direct Mode\).*?\n', '', content, flags=re.DOTALL)

        # Extract and preserve contact information
        contact_info = []
        address_pattern = r'\b\d+\s+[A-Za-z\s]+(?:St|Street|Ave|Avenue|Rd|Road|Blvd|Boulevard|Dr|Drive),\s+[A-Za-z\s]+,\s+[A-Z]{2}\s+\d{5}\b'
        phone_pattern = r'\b(?:\+?1[-.\s]?)?(?:\(\d{3}\)|\d{3})[-.\s]?\d{3}[-.\s]?\d{4}\b'

        # Find all addresses and phone numbers in the content
        addresses = re.findall(address_pattern, content)
        phones = re.findall(phone_pattern, content)

        # Store unique contact information (avoid duplicates)
        unique_addresses = set(addresses)
        unique_phones = set(phones)

        for address in unique_addresses:
            contact_info.append(f"Address: {address}")
        for phone in unique_phones:
            contact_info.append(f"Phone: {phone}")

        # Split content by page separator
        page_separator = "=" * 80
        pages = re.split(f"(?m)^{page_separator}$", content)

        # Process each page to remove navigation links and keep main content
        processed_pages = []

        for page in pages:
            if not page.strip():
                continue

            # Extract URL and title for context
            url_match = re.search(r'URL:\s*(.*?)$', page, re.MULTILINE)
            title_match = re.search(r'TITLE:\s*(.*?)$', page, re.MULTILINE)

            url = url_match.group(1) if url_match else None
            title = title_match.group(1) if title_match else None

            # Split page into lines for processing
            lines = page.strip().split('\n')

            # Process the lines to remove navigation elements
            processed_lines = self._process_page_lines(lines)

            # Only add pages that have content after processing
            if processed_lines:
                # Add page header with URL and title for context
                page_header = []
                if url:
                    page_header.append(f"Page: {url}")
                if title:
                    page_header.append(f"Title: {title}")

                # Combine header and processed content
                if page_header:
                    processed_page = "\n".join(page_header) + "\n\n" + "\n".join(processed_lines)
                else:
                    processed_page = "\n".join(processed_lines)

                processed_pages.append(processed_page)

        # Join processed pages with clear separators
        preprocessed_content = "\n\n---\n\n".join(processed_pages)

        # Join lines that appear to be part of the same sentence or paragraph
        # Look for lines ending without punctuation and followed by a line starting with lowercase
        preprocessed_content = re.sub(r'([^.!?:;])\n([a-z])', r'\1 \2', preprocessed_content)

        # Remove all heading markers (####) while preserving the heading text
        preprocessed_content = re.sub(r'#+\s*(.*?)\n', r'\1\n', preprocessed_content, flags=re.MULTILINE)

        # Add the preserved contact information at the end if not already present
        if contact_info and not "Contact Information" in preprocessed_content:
            preprocessed_content += "\n\n--- Contact Information ---\n"
            preprocessed_content += "\n".join(contact_info)

        # Clean HTML entity codes in titles (e.g., &8211;)
        preprocessed_content = re.sub(r'&\d+;', ' - ', preprocessed_content)

        # Standardize hours format and remove duplicate hours listings
        hours_pattern = r'Mon-Fri:\s*8:00am\s*[–-]\s*6:00pm\s*\n\s*Sat:\s*Closed\s*\n\s*Sun:\s*Closed'
        preprocessed_content = re.sub(hours_pattern, 'Hours: Monday-Friday 8:00am-6:00pm, Saturday-Sunday Closed', preprocessed_content)

        # Remove page separators and URL/title headers
        # This pattern matches:
        # 1. The "Page:" line (with or without URL)
        # 2. The "Title:" line with text
        # 3. The "URL:" line (with or without URL)
        # 4. The "TITLE:" line with text
        # 5. The "---" separator line
        page_header_pattern = r'(?:Page:.*?\n)(?:Title:.*?\n)(?:URL:.*?\n)(?:TITLE:.*?\n)(?:---\s*\n)'
        preprocessed_content = re.sub(page_header_pattern, '', preprocessed_content, flags=re.DOTALL)

        # Also handle cases where some header lines might be missing
        preprocessed_content = re.sub(r'Page:.*?\n', '', preprocessed_content)
        preprocessed_content = re.sub(r'Title:.*?\n', '', preprocessed_content)
        preprocessed_content = re.sub(r'URL:.*?\n', '', preprocessed_content)
        preprocessed_content = re.sub(r'TITLE:.*?\n', '', preprocessed_content)

        # Remove any remaining separator lines
        preprocessed_content = re.sub(r'---\s*\n', '', preprocessed_content)

        # Remove URLs that appear in the text content using a general pattern
        # This matches any URL starting with http:// or https:// followed by domain and path
        url_pattern = r'https?://(?:www\.)?[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)+(?:/[a-zA-Z0-9._~:/?#[\]@!$&\'()*+,;=%-]+)*'
        preprocessed_content = re.sub(url_pattern, '', preprocessed_content)

        # Join short lines (less than 5 words) with the following line
        # This is done in multiple passes to handle consecutive short lines
        for _ in range(3):  # Multiple passes to handle consecutive short lines
            # This pattern finds lines with 1-4 words followed by a newline and another line
            # It captures the short line, the newline, and the next line, then joins them with a space
            preprocessed_content = re.sub(r'([^\n]+?(?:\s+[^\s\n]+){0,3})\n([^\n]+)', r'\1 \2', preprocessed_content)

        # Remove all empty lines completely
        lines = preprocessed_content.split('\n')
        non_empty_lines = [line for line in lines if line.strip()]
        preprocessed_content = '\n'.join(non_empty_lines)

        # Final cleanup - ensure no empty lines remain at the beginning or end
        preprocessed_content = preprocessed_content.strip()

        # Print token reduction stats
        original_tokens = len(content.split())
        preprocessed_tokens = len(preprocessed_content.split())
        reduction_percent = ((original_tokens - preprocessed_tokens) / original_tokens) * 100 if original_tokens > 0 else 0
        print(f"Content preprocessing: Reduced token count from {original_tokens} to {preprocessed_tokens} tokens ({reduction_percent:.1f}% reduction)")

        return preprocessed_content

    def _process_page_lines(self, lines):
        """
        Process the lines of a page to remove navigation elements while preserving content.

        Args:
            lines: List of lines from a page

        Returns:
            List of processed lines with navigation elements removed
        """
        processed_lines = []
        skip_until_empty_line = False
        in_accessibility_toolbar = False

        # Define patterns for navigation elements
        nav_patterns = [
            # Main navigation menu pattern (list items with links)
            (r'^\s*\*\s+\[.*?\]\(http.*?\)\s*$', "Main navigation menu item"),

            # Social media links pattern
            (r'^\s*\[\]\(http.*?(facebook|twitter|instagram|linkedin).*?\).*$', "Social media link"),

            # Accessibility toolbar patterns
            (r'^\s*Accessibility Toolbar\s*$', "Accessibility toolbar header"),
            (r'^\s*_(keyboard|close|visibility_off|nights_stay|format_size|text_fields|font_download|title|link)_.*$', "Accessibility control"),
            (r'^\s*Toggle the visibility of the Accessibility Toolbar\s*$', "Accessibility toggle"),

            # Footer attribution pattern
            (r'^\s*Powered with.*?by \[.*?\].*$', "Footer attribution"),

            # Copyright pattern
            (r'^\s*©\s*\d{4}.*$', "Copyright notice"),

            # Gallery navigation pattern
            (r'^\s*\d+\s*/\s*\d+\s*$', "Gallery pagination"),

            # Back to blog/list navigation
            (r'^\s*\[Back to the.*?\]\(.*?\)\s*$', "Back navigation")
        ]

        # Important content keywords to preserve sections containing them
        important_keywords = [
            'service', 'treatment', 'appointment', 'contact', 'about', 'hour',
            'location', 'staff', 'doctor', 'specialist', 'emergency', 'policy',
            'cancellation', 'mission', 'vision', 'pet', 'animal', 'care',
            'wellness', 'health', 'vaccine', 'dental', 'surgery', 'exam'
        ]

        # Process each line
        i = 0
        while i < len(lines):
            line = lines[i]

            # Skip empty lines - don't add them to processed_lines to remove all empty lines
            if not line.strip():
                i += 1
                continue

            # Check if we're entering an accessibility toolbar section
            if re.match(r'^\s*Accessibility Toolbar\s*$', line, re.IGNORECASE):
                in_accessibility_toolbar = True
                i += 1
                continue

            # If we're in an accessibility toolbar section, skip until we find an empty line
            if in_accessibility_toolbar:
                if not line.strip():
                    in_accessibility_toolbar = False
                i += 1
                continue

            # Check if we should skip until an empty line (for multi-line navigation elements)
            if skip_until_empty_line:
                if not line.strip():
                    skip_until_empty_line = False
                i += 1
                continue

            # Check if line matches any navigation pattern
            is_navigation = False
            for pattern, description in nav_patterns:
                if re.match(pattern, line, re.IGNORECASE):
                    is_navigation = True
                    break

            # Skip navigation elements
            if is_navigation:
                i += 1
                continue

            # Check for image references (but keep images that might be content-relevant)
            if re.match(r'^\s*!\[.*?\]\(.*?\)\s*$', line):
                # Skip standalone image references that are likely decorative
                if i > 0 and i < len(lines) - 1:
                    prev_line = lines[i-1].strip()
                    next_line = lines[i+1].strip()

                    # If the image is between content lines, it's likely relevant
                    if prev_line and next_line and not re.match(r'^\s*!\[.*?\]\(.*?\)\s*$', prev_line) and not re.match(r'^\s*!\[.*?\]\(.*?\)\s*$', next_line):
                        processed_lines.append(line)
                i += 1
                continue

            # Check for clickable images (format: [![Image text](image_url)](link_url))
            if re.match(r'^\s*\[!\[.*?\]\(.*?\)\]\(.*?\)\s*$', line):
                i += 1
                continue

            # Check for lines that are just separator characters
            if re.match(r'^[=\-_]{3,}$', line.strip()):
                i += 1
                continue

            # Preserve the line if it contains important content
            contains_important_content = any(keyword in line.lower() for keyword in important_keywords)

            # Check surrounding context for section importance
            context_importance = 0
            context_window = 3  # Check 3 lines before and after

            for j in range(max(0, i-context_window), min(len(lines), i+context_window+1)):
                if j != i and any(keyword in lines[j].lower() for keyword in important_keywords):
                    context_importance += 1

            # If the line or its context contains important content, preserve it
            if contains_important_content or context_importance > 0:
                processed_lines.append(line)
            # Otherwise, apply more scrutiny to determine if it's content or navigation
            else:
                # Check if it's a list item with a link (potential navigation)
                is_list_item_with_link = (line.strip().startswith('*') or line.strip().startswith('-')) and '[' in line and '](' in line

                # Check if it's a short line with a link (potential navigation)
                is_short_link = '[' in line and '](' in line and len(line.strip()) < 40

                # If it looks like navigation, skip it
                if is_list_item_with_link or is_short_link:
                    i += 1
                    continue

                # Otherwise, preserve the line as content
                processed_lines.append(line)

            i += 1

        # Remove any remaining empty lines at the beginning or end
        while processed_lines and not processed_lines[0].strip():
            processed_lines.pop(0)
        while processed_lines and not processed_lines[-1].strip():
            processed_lines.pop()

        return processed_lines

    def parse_content(self, content: str, save_preprocessed: bool = False, preprocessed_file: str = None, skip_preprocessing: bool = False, homepage_url: str = None) -> Dict:
        """
        Parse crawled content into structured data using Gemini API

        Args:
            content: Raw crawled content
            save_preprocessed: Whether to save the preprocessed content to a file
            preprocessed_file: Path to save the preprocessed content (if None, will use a default path)
            skip_preprocessing: Whether to skip content preprocessing (keep all links)
            homepage_url: The homepage URL to include in the prompt (even if removed during preprocessing)

        Returns:
            Structured data extracted from the content
        """
        # Preprocess content to reduce token count (unless skipped)
        if skip_preprocessing:
            print("Skipping content preprocessing (using raw content)")
            preprocessed_content = content
        else:
            preprocessed_content = self._preprocess_content(content)

        # Save preprocessed content if requested (for debugging)
        if save_preprocessed:
            if not preprocessed_file:
                # Create a default filename
                import tempfile
                preprocessed_file = os.path.join(tempfile.gettempdir(), "preprocessed_content.txt")

            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(preprocessed_file), exist_ok=True)

            # Save the preprocessed content
            with open(preprocessed_file, 'w', encoding='utf-8') as f:
                f.write(preprocessed_content)
            print(f"Preprocessed content saved to {preprocessed_file}")

        # Create prompt with preprocessed content and homepage URL
        prompt = self._create_prompt(preprocessed_content, homepage_url)

        try:
            # Set up API endpoint
            api_url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key={self.api_key}"
            headers = {"Content-Type": "application/json"}

            # Prepare request data
            data = {
                "contents": [{"parts": [{"text": prompt}]}],
                "generationConfig": {
                    "maxOutputTokens": 8000,
                    "temperature": 0.2
                }
            }

            # Set up session with retries
            session = requests.Session()
            retries = Retry(total=3, backoff_factor=1, status_forcelist=[429, 500, 502, 503, 504], allowed_methods=["POST"])
            session.mount('https://', HTTPAdapter(max_retries=retries))

            # Make the request
            print("Sending request to Gemini API...")
            response = session.post(api_url, headers=headers, json=data, timeout=60)

            if response.status_code != 200:
                print(f"Gemini API request failed: {response.status_code} - {response.text}")
                return {}

            # Parse the response
            result = response.json()
            if not result.get('candidates'):
                print("Gemini API returned no candidates")
                return {}

            # Extract the generated text
            generated_text = result.get('candidates', [{}])[0].get('content', {}).get('parts', [{}])[0].get('text', '').strip()

            # Clean up the response if it's wrapped in markdown code blocks
            cleaned_text = generated_text
            if generated_text.startswith('```') and generated_text.endswith('```'):
                # Remove the markdown code block formatting
                lines = generated_text.split('\n')
                if len(lines) > 2:  # At least 3 lines (opening ```, content, closing ```)
                    # Remove first and last line
                    cleaned_text = '\n'.join(lines[1:-1])

            # If it starts with ```json or similar, remove that too
            if cleaned_text.startswith('```'):
                cleaned_text = '\n'.join(cleaned_text.split('\n')[1:])

            # Parse the JSON response
            try:
                # First try to parse the JSON as is
                try:
                    structured_data = json.loads(cleaned_text)
                    return structured_data
                except json.JSONDecodeError:
                    # If that fails, check if the response is truncated
                    print("Initial JSON parsing failed, checking for truncated response...")

                    # Try to fix truncated JSON by adding missing closing braces
                    if cleaned_text.count('{') > cleaned_text.count('}'):
                        # Count how many opening braces we need to close
                        missing_braces = cleaned_text.count('{') - cleaned_text.count('}')
                        fixed_text = cleaned_text + ('}' * missing_braces)
                        try:
                            structured_data = json.loads(fixed_text)
                            print("Successfully fixed truncated JSON response")
                            return structured_data
                        except json.JSONDecodeError:
                            pass  # If this also fails, continue to the next approach

                    # If we can't fix it, try to extract partial data
                    print("Error: Failed to parse Gemini response as JSON")
                    print(f"Response text: {cleaned_text[:500]}...")  # Print first 500 chars for debugging

                    # Create a minimal valid response with the data we can extract
                    partial_data = {
                        "name": None,
                        "address": [],
                        "homepage": None,
                        "phone_numbers": [],
                        "opening_hours": {},
                        "staff": [],
                        "overview": "Data extraction failed due to API response issues. Please check the raw content file for details.",
                        "animals_treated": [],
                        "animals_not_treated": [],
                        "services": [],
                        "location_specific": False,
                        "emergency": False,
                        "emergency_details": None,
                        "partial_data": True  # Flag to indicate this is partial data
                    }

                    # Try to extract some basic information using regex patterns
                    try:
                        # Extract name if present
                        name_match = re.search(r'"name"\s*:\s*"([^"]+)"', cleaned_text)
                        if name_match:
                            partial_data["name"] = name_match.group(1)

                        # Extract homepage if present
                        homepage_match = re.search(r'"homepage"\s*:\s*"([^"]+)"', cleaned_text)
                        if homepage_match:
                            partial_data["homepage"] = homepage_match.group(1)

                        # Extract phone numbers if present
                        phone_match = re.search(r'"phone_numbers"\s*:\s*\[(.*?)\]', cleaned_text, re.DOTALL)
                        if phone_match:
                            phones_text = phone_match.group(1)
                            phones = re.findall(r'"([^"]+)"', phones_text)
                            if phones:
                                partial_data["phone_numbers"] = phones

                        print("Created partial data from truncated response")
                    except Exception as e:
                        print(f"Error extracting partial data: {e}")

                    return partial_data
            except Exception as e:
                print(f"Unexpected error parsing JSON: {e}")
                return {}
        except Exception as e:
            print(f"Error generating content with Gemini API: {e}")
            return {}

    def _create_prompt(self, content: str, homepage_url: str = None) -> str:
        """Create a structured prompt for the Gemini model with location context if available

        Args:
            content: The preprocessed content to include in the prompt
            homepage_url: The homepage URL to include in the prompt (even if removed during preprocessing)
        """
        prompt = """
You are a specialized data extraction assistant. Extract ONLY the following information from the provided website content into a JSON structure:

1. name: The business name
2. address: format adress to the structure in the example output format
3. homepage: Website URL
4. phone_numbers: List of phone numbers
5. opening_hours: Business hours in structured format
6. staff: Brief info about staff members (name, role, brief description)
7. overview: A short overview of the business (100 words max)
8. animals_treated: List all animals they treat
9. animals_not_treated: List of animals explicitly not treated
10. services: List of services they offer
11. location_specific: true/false based if its same location clinic and not just other location of same brand
12. emergency: true/false
13. emergency_details: provide all details about the emergency services you can find in text.
14. appointment: link and/or info about appointments

IMPORTANT:
- Return ONLY valid JSON with these fields
- Use null for missing information
- Do not include any explanations or additional text
- Keep descriptions concise
- Format phone numbers consistently
- Format opening hours as structured data
- Format animals to be just a list of species if specified and not only broad term is used. if only broad terms are used, use them as a list.
"""

        # Add location-specific context if available
        if self.location_info:
            location_context = f"""
SPECIFIC LOCATION CONTEXT:
I'm specifically looking for information about this location:
- Clinic Name: {self.location_info.get('Name', 'Unknown')}
- Address: {self.location_info.get('Formatted Address', 'Unknown')}
- Phone: {self.location_info.get('Phone Number', 'Unknown')}

If this website covers multiple locations, ONLY extract information for THIS SPECIFIC LOCATION.
If you can't find information specific to this location, extract general information but note in "location_specific":  false.
"""
            prompt += location_context

        # Add homepage URL information if provided
        if homepage_url:
            prompt += f"\n\nIMPORTANT: The website homepage URL is: {homepage_url}"
            prompt += "\nMake sure to include this URL in the 'homepage' field of your JSON output."

        prompt += "\n\nExample output format:\n"
        prompt += """{
  "name": "Business Name",
  "address": ["123 Main St.", "MILWAUKEE", "WI", "53204"],
  "homepage": "https://example.com",
  "phone_numbers": ["************"],
  "opening_hours": {
    "monday": "8:00am - 6:00pm",
    "tuesday": "8:00am - 6:00pm",
    "wednesday": "8:00am - 6:00pm",
    "thursday": "8:00am - 6:00pm",
    "friday": "8:00am - 6:00pm",
    "saturday": "Closed",
    "sunday": "Closed"
  },
  "staff": [
    {"name": "John Doe", "role": "Owner", "description": "Founded the business in 2010."},
    {"name": "Jane Smith", "role": "Manager", "description": "Oversees daily operations."}
  ],
  "overview": "Brief description of the business and its services.",
  "animals_treated": ["Dogs", "Cats", "Birds", "Small mammals"],
  "animals_not_treated": ["Exotic animals", "Large livestock"],
  "services": ["Wellness exams", "Vaccinations", "Surgery", "Dental care"],
  "location_specific": true/false,
  "emergency": true,
  "emergency_details": "All info about emergency services even if they dont offer emergency services because they may refer to another hospital",
  "appointment": "link and/or info about appointments"

}
"""
        prompt += content
        return prompt
