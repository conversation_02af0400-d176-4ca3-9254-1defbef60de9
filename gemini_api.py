import os
import json
import requests
import re
from requests.adapters import H<PERSON><PERSON>dapter
from urllib3.util.retry import Retry
import backoff
from dotenv import load_dotenv

# Load environment variables
load_dotenv()
GEMINI_API_KEY = os.getenv('GEMINI_API_KEY')

def clean_json_data(data):
    """
    Clean JSON data by removing or replacing special characters in string values.

    Args:
        data: The JSON data to clean (dict, list, or primitive type)

    Returns:
        The cleaned JSON data
    """
    if isinstance(data, dict):
        # Process each key-value pair in the dictionary
        return {k: clean_json_data(v) for k, v in data.items()}
    elif isinstance(data, list):
        # Process each item in the list
        return [clean_json_data(item) for item in data]
    elif isinstance(data, str):
        # Clean string values
        # 1. Replace newlines with spaces
        cleaned = data.replace('\n', ' ').replace('\r', ' ')
        # 2. Replace multiple spaces with a single space
        cleaned = re.sub(r'\s+', ' ', cleaned)
        # 3. Remove any other control characters
        cleaned = re.sub(r'[\x00-\x1F\x7F]', '', cleaned)
        # 4. Trim leading/trailing whitespace
        cleaned = cleaned.strip()
        return cleaned
    else:
        # Return other types (numbers, booleans, None) unchanged
        return data

@backoff.on_exception(
    backoff.expo,
    (requests.exceptions.RequestException, json.JSONDecodeError),
    max_tries=3,
    max_time=30,
    on_backoff=lambda details: print(f"Retrying Gemini API call (attempt {details['tries']} after {details['wait']}s wait)")
)
def call_gemini_api(prompt_text, max_output_tokens=8000, temperature=0.2, output_file=None):
    """
    Function to call Gemini API with a text prompt and save results to a file.

    Args:
        prompt_text: The text prompt to send to Gemini
        max_output_tokens: Maximum number of tokens in the response (default: 8000)
        temperature: Temperature for response generation (default: 0.2)
        output_file: Optional file path to save the response

    Returns:
        str: The generated text response
    """
    try:
        # Set up API endpoint
        api_url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key={GEMINI_API_KEY}"
        headers = {"Content-Type": "application/json"}

        # Prepare request data with increased token limit
        data = {
            "contents": [{"parts": [{"text": prompt_text}]}],
            "generationConfig": {
                "maxOutputTokens": max_output_tokens,
                "temperature": temperature
            }
        }

        # Set up session with retries
        session = requests.Session()
        retries = Retry(total=3, backoff_factor=1, status_forcelist=[429, 500, 502, 503, 504], allowed_methods=["POST"])
        session.mount('https://', HTTPAdapter(max_retries=retries))

        # Make the request with increased timeout
        print("Sending request to Gemini API...")
        response = session.post(api_url, headers=headers, json=data, timeout=60)

        if response.status_code != 200:
            print(f"Gemini API request failed: {response.status_code} - {response.text}")
            return None

        # Parse the response
        result = response.json()
        if not result.get('candidates'):
            print("Gemini API returned no candidates")
            return None

        # Extract the generated text
        generated_text = result.get('candidates', [{}])[0].get('content', {}).get('parts', [{}])[0].get('text', '').strip()

        # Clean up the response if it's wrapped in markdown code blocks
        cleaned_text = generated_text
        if generated_text.startswith('```') and generated_text.endswith('```'):
            # Remove the markdown code block formatting
            lines = generated_text.split('\n')
            if len(lines) > 2:  # At least 3 lines (opening ```, content, closing ```)
                # Remove first and last line
                cleaned_text = '\n'.join(lines[1:-1])

        # If it starts with ```json or similar, remove that too
        if cleaned_text.startswith('```'):
            cleaned_text = '\n'.join(cleaned_text.split('\n')[1:])

        # Save to file if output_file is provided
        if output_file and generated_text:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(output_file), exist_ok=True)

            # Save both the original and cleaned text
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(generated_text)

            # Save cleaned version if different
            if cleaned_text != generated_text:
                # Make sure the cleaned file goes to the same directory as the raw file
                clean_output_file = output_file.replace('_raw.txt', '_cleaned.txt')
                with open(clean_output_file, 'w', encoding='utf-8') as f:
                    f.write(cleaned_text)
                print(f"Response saved to {output_file} (cleaned version at {clean_output_file})")
            else:
                print(f"Response saved to {output_file}")

        # Return the cleaned text for processing
        return cleaned_text

    except Exception as e:
        print(f"Error calling Gemini API: {str(e)}")
        return None
