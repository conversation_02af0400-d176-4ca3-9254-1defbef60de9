#!/usr/bin/env python3
"""
Processing tracker for the parser.
This module provides a class for tracking the processing status of websites.
"""

import os
import json
import glob
import fcntl
import errno
from datetime import datetime
from typing import Dict, List, Optional, Any, Set, Tuple

class StatusCode:
    """Status codes for processing stages"""
    SUCCESS = "success"
    FAILURE = "failure"
    PARTIAL = "partial"
    IN_PROGRESS = "in_progress"
    NOT_STARTED = "not_started"

class ProcessingStage:
    """Processing stages"""
    CRAWLING = "crawling"
    PREPROCESSING = "preprocessing"
    PARSING = "parsing"
    COMPLETED = "completed"

class ProcessingTracker:
    """
    Class for tracking the processing status of websites.
    This extends the FailureTracker to provide more detailed status tracking.
    """

    def __init__(self, output_dir: str = "failed_sites",
                 parsed_dir: str = "parsed_results",
                 status_dir: Optional[str] = None,
                 locks_dir: Optional[str] = None):
        """
        Initialize the processing tracker.

        Args:
            output_dir: Directory to store failure logs
            parsed_dir: Directory containing parsed results
            status_dir: Directory to store status files (default: output_dir/status)
            locks_dir: Directory to store lock files (default: output_dir/locks)
        """
        self.output_dir = output_dir
        self.parsed_dir = parsed_dir
        self.status_dir = status_dir or os.path.join(output_dir, "status")
        self.locks_dir = locks_dir or os.path.join(output_dir, "locks")

        # Create directories if they don't exist
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(self.status_dir, exist_ok=True)
        os.makedirs(self.locks_dir, exist_ok=True)

        # Load previously failed domains
        self.failed_domains = self._load_failed_domains()
        print(f"Loaded {len(self.failed_domains)} previously failed domains")

        # Load previously crawled domains
        self.crawled_domains = self._load_crawled_domains()
        print(f"Loaded {len(self.crawled_domains)} previously crawled domains")

        # Load processing status
        self.processing_status = self._load_processing_status()
        print(f"Loaded processing status for {len(self.processing_status)} websites")

    def _load_failed_domains(self) -> Set[str]:
        """
        Load previously failed domains from the failure log.

        Returns:
            Set of failed domains
        """
        failed_domains = set()
        failure_log = os.path.join(self.output_dir, "failed_websites.txt")

        if os.path.exists(failure_log):
            with open(failure_log, 'r', encoding='utf-8') as f:
                for line in f:
                    domain = line.strip()
                    if domain:
                        failed_domains.add(domain)

        return failed_domains

    def _load_crawled_domains(self) -> Set[str]:
        """
        Load previously crawled domains from the crawled content directory.

        Returns:
            Set of crawled domains
        """
        crawled_domains = set()
        crawled_dir = os.path.join(self.parsed_dir, "raw")

        if os.path.exists(crawled_dir):
            for filename in os.listdir(crawled_dir):
                if filename.endswith("_crawled.txt"):
                    domain = filename.split("_")[0]
                    crawled_domains.add(domain)

        return crawled_domains

    def _load_processing_status(self) -> Dict[str, Dict]:
        """
        Load processing status from status files.

        Returns:
            Dictionary mapping URLs to status dictionaries
        """
        processing_status = {}

        # Load from the main status file
        status_file = os.path.join(self.status_dir, "_status.json")
        if os.path.exists(status_file):
            try:
                with open(status_file, 'r', encoding='utf-8') as f:
                    status_data = json.load(f)

                for url, status in status_data.items():
                    if isinstance(status, dict):
                        processing_status[url] = status
            except Exception as e:
                print(f"Warning: Could not load status file {status_file}: {e}")

        # Load from individual status files
        for status_file in glob.glob(os.path.join(self.status_dir, "*_status.json")):
            if os.path.basename(status_file) == "_status.json":
                continue  # Skip the main status file

            try:
                with open(status_file, 'r', encoding='utf-8') as f:
                    status_data = json.load(f)

                for url, status in status_data.items():
                    if isinstance(status, dict):
                        processing_status[url] = status
            except Exception as e:
                print(f"Warning: Could not load status file {status_file}: {e}")

        return processing_status

    def update_status(self, url: str, stage: str, status: str, details: str = "") -> None:
        """
        Update the processing status for a website.

        Args:
            url: URL of the website
            stage: Processing stage (crawling, preprocessing, parsing, completed)
            status: Status code (success, failure, partial, in_progress, not_started)
            details: Additional details about the status
        """
        # Get the domain from the URL
        domain = url.split("//")[-1].split("/")[0] if "//" in url else url
        if domain.startswith("www."):
            domain = domain[4:]

        # Get the status file path
        status_file = os.path.join(self.status_dir, f"{domain}_status.json")

        # Load existing status data
        status_data = {}
        if os.path.exists(status_file):
            try:
                with open(status_file, 'r', encoding='utf-8') as f:
                    status_data = json.load(f)
            except Exception as e:
                print(f"Warning: Could not load status file {status_file}: {e}")

        # Update or create status entry
        if url not in status_data:
            status_data[url] = {
                "url": url,
                "domain": domain,
                "first_seen": datetime.now().isoformat(),
                "stages": {},
                "overall_status": StatusCode.NOT_STARTED,
                "last_updated": datetime.now().isoformat()
            }

        # Update stage status
        if "stages" not in status_data[url]:
            status_data[url]["stages"] = {}

        status_data[url]["stages"][stage] = {
            "status": status,
            "timestamp": datetime.now().isoformat(),
            "details": details
        }

        # Update overall status
        if stage == ProcessingStage.COMPLETED:
            status_data[url]["overall_status"] = status
        elif status == StatusCode.FAILURE:
            status_data[url]["overall_status"] = StatusCode.FAILURE
        elif status == StatusCode.PARTIAL and status_data[url]["overall_status"] != StatusCode.FAILURE:
            status_data[url]["overall_status"] = StatusCode.PARTIAL
        elif status == StatusCode.IN_PROGRESS and status_data[url]["overall_status"] not in [StatusCode.FAILURE, StatusCode.PARTIAL]:
            status_data[url]["overall_status"] = StatusCode.IN_PROGRESS

        # Update last updated timestamp
        status_data[url]["last_updated"] = datetime.now().isoformat()

        # Save status data
        try:
            with open(status_file, 'w', encoding='utf-8') as f:
                json.dump(status_data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Warning: Could not save status file {status_file}: {e}")

        # Update the main status file
        self._update_main_status_file(url, status_data[url])

        # Update the in-memory status
        self.processing_status[url] = status_data[url]

        # Log the status update
        print(f"[{datetime.now().isoformat()}] {url} - {stage}: {status} - {details}")

    def _update_main_status_file(self, url: str, status: Dict) -> None:
        """
        Update the main status file with the status for a website.

        Args:
            url: URL of the website
            status: Status dictionary
        """
        status_file = os.path.join(self.status_dir, "_status.json")

        # Load existing status data
        status_data = {}
        if os.path.exists(status_file):
            try:
                with open(status_file, 'r', encoding='utf-8') as f:
                    status_data = json.load(f)
            except Exception as e:
                print(f"Warning: Could not load main status file: {e}")

        # Update status entry
        status_data[url] = status

        # Save status data
        try:
            with open(status_file, 'w', encoding='utf-8') as f:
                json.dump(status_data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Warning: Could not save main status file: {e}")

    def try_acquire_lock(self, url: str):
        """
        Try to acquire a lock for processing a website.

        Args:
            url: URL of the website

        Returns:
            File descriptor if the lock was acquired, False otherwise
        """
        # Get the domain from the URL
        domain = url.split("//")[-1].split("/")[0] if "//" in url else url
        if domain.startswith("www."):
            domain = domain[4:]

        # Get the lock file path
        lock_file = os.path.join(self.locks_dir, f"{domain}.lock")

        try:
            # Create the lock file if it doesn't exist
            fd = os.open(lock_file, os.O_CREAT | os.O_WRONLY)

            # Try to acquire an exclusive lock
            fcntl.flock(fd, fcntl.LOCK_EX | fcntl.LOCK_NB)

            # Write the current timestamp to the lock file
            os.write(fd, datetime.now().isoformat().encode('utf-8'))

            # Keep the file descriptor open to maintain the lock
            return fd
        except IOError as e:
            if e.errno == errno.EACCES or e.errno == errno.EAGAIN:
                # Lock already held by another process
                return False
            else:
                # Some other error
                print(f"Warning: Could not acquire lock for {url}: {e}")
                return False

    def acquire_lock(self, url: str) -> bool:
        """
        Acquire a lock for processing a website.

        Args:
            url: URL of the website

        Returns:
            True if the lock was acquired, False otherwise
        """
        # Get the domain from the URL
        domain = url.split("//")[-1].split("/")[0] if "//" in url else url
        if domain.startswith("www."):
            domain = domain[4:]

        # Get the lock file path
        lock_file = os.path.join(self.locks_dir, f"{domain}.lock")

        try:
            # Create the lock file if it doesn't exist
            fd = os.open(lock_file, os.O_CREAT | os.O_WRONLY)

            # Try to acquire an exclusive lock
            fcntl.flock(fd, fcntl.LOCK_EX | fcntl.LOCK_NB)

            # Write the current timestamp to the lock file
            os.write(fd, datetime.now().isoformat().encode('utf-8'))

            # Keep the file descriptor open to maintain the lock
            return fd
        except IOError as e:
            if e.errno == errno.EACCES or e.errno == errno.EAGAIN:
                # Lock already held by another process
                return False
            else:
                # Some other error
                print(f"Warning: Could not acquire lock for {url}: {e}")
                return False

    def release_lock(self, fd: int) -> None:
        """
        Release a lock for processing a website.

        Args:
            fd: File descriptor returned by acquire_lock
        """
        if fd:
            try:
                # Release the lock
                fcntl.flock(fd, fcntl.LOCK_UN)

                # Close the file descriptor
                os.close(fd)
            except Exception as e:
                print(f"Warning: Could not release lock: {e}")

    def add_crawl_failure(self, url: str, error_msg: str) -> None:
        """
        Add a crawl failure to the tracker.

        Args:
            url: URL of the website
            error_msg: Error message
        """
        # Get the domain from the URL
        domain = url.split("//")[-1].split("/")[0] if "//" in url else url
        if domain.startswith("www."):
            domain = domain[4:]

        # Add to failed domains
        self.failed_domains.add(domain)

        # Update status to indicate crawling failed
        self.update_status(
            url,
            ProcessingStage.CRAWLING,
            StatusCode.FAILURE,
            details=f"Crawling failed: {error_msg}"
        )

    def add_parse_failure(self, url: str, error_msg: str) -> None:
        """
        Add a parse failure to the tracker.

        Args:
            url: URL of the website
            error_msg: Error message
        """
        # Get the domain from the URL
        domain = url.split("//")[-1].split("/")[0] if "//" in url else url
        if domain.startswith("www."):
            domain = domain[4:]

        # Add to failed domains
        self.failed_domains.add(domain)

        # Update status to indicate parsing failed
        self.update_status(
            url,
            ProcessingStage.PARSING,
            StatusCode.FAILURE,
            details=f"Parsing failed: {error_msg}"
        )

    def has_failures(self) -> bool:
        """
        Check if there are any failures.

        Returns:
            True if there are failures, False otherwise
        """
        return len(self.failed_domains) > 0

    def get_all_failed_urls(self) -> List[str]:
        """
        Get all failed URLs.

        Returns:
            List of failed URLs
        """
        return list(self.failed_domains)

    def save_failures(self) -> str:
        """
        Save failures to a JSON file.

        Returns:
            Path to the JSON file
        """
        failures_file = os.path.join(self.output_dir, "failed_websites.json")

        with open(failures_file, 'w', encoding='utf-8') as f:
            json.dump(list(self.failed_domains), f, indent=2, ensure_ascii=False)

        return failures_file

    def save_as_text(self) -> str:
        """
        Save failures to a text file.

        Returns:
            Path to the text file
        """
        failures_file = os.path.join(self.output_dir, "failed_websites.txt")

        with open(failures_file, 'w', encoding='utf-8') as f:
            for domain in sorted(self.failed_domains):
                f.write(f"{domain}\n")

        return failures_file

    def check_website_status(self, url: str) -> Tuple[bool, str]:
        """
        Check if a website should be skipped based on previous crawls or failures.

        Args:
            url: URL of the website

        Returns:
            Tuple of (should_skip, reason)
        """
        # Get the domain from the URL
        domain = url.split("//")[-1].split("/")[0] if "//" in url else url
        if domain.startswith("www."):
            domain = domain[4:]

        # Check if the domain has previously failed
        if domain in self.failed_domains:
            return True, "Previously failed"

        # Check if the domain has been previously crawled
        if domain in self.crawled_domains:
            return True, "Previously crawled"

        return False, ""

    def generate_status_report(self) -> Dict[str, Any]:
        """
        Generate a comprehensive status report.

        Returns:
            Dictionary with status report data
        """
        total_sites = len(self.processing_status)
        completed_sites = 0
        failed_sites = 0
        partial_sites = 0
        in_progress_sites = 0
        not_started_sites = 0

        # Group sites by status
        sites_by_status = {
            "completed": [],
            "failed": [],
            "partial": [],
            "in_progress": [],
            "not_started": []
        }

        # Process each status entry
        for url, status in self.processing_status.items():
            # Skip if this is a string (lock file descriptor)
            if isinstance(status, str):
                continue

            # Get the overall status
            overall_status = status.get("overall_status", StatusCode.NOT_STARTED)

            # Count by status
            if overall_status == StatusCode.SUCCESS:
                completed_sites += 1
                sites_by_status["completed"].append(url)
            elif overall_status == StatusCode.FAILURE:
                failed_sites += 1
                sites_by_status["failed"].append(url)
            elif overall_status == StatusCode.PARTIAL:
                partial_sites += 1
                sites_by_status["partial"].append(url)
            elif overall_status == StatusCode.IN_PROGRESS:
                in_progress_sites += 1
                sites_by_status["in_progress"].append(url)
            else:
                not_started_sites += 1
                sites_by_status["not_started"].append(url)

        # Create the report
        report = {
            "generated_at": datetime.now().isoformat(),
            "summary": {
                "total_sites": total_sites,
                "completed_sites": completed_sites,
                "failed_sites": failed_sites,
                "partial_sites": partial_sites,
                "in_progress_sites": in_progress_sites,
                "not_started_sites": not_started_sites,
                "success_rate": (completed_sites / total_sites * 100) if total_sites > 0 else 0
            },
            "sites_by_status": sites_by_status,
            "detailed_status": self.processing_status
        }

        return report

    def save_status_report(self, filename: Optional[str] = None) -> str:
        """
        Generate and save a status report to a file.

        Args:
            filename: Optional custom filename

        Returns:
            Path to the saved file
        """
        if not filename:
            timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
            filename = f"status_report_{timestamp}.json"

        filepath = os.path.join(self.status_dir, filename)

        # Generate the report
        report = self.generate_status_report()

        # Save to file
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)

        print(f"Status report saved to {filepath}")
        return filepath
