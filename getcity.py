import os
import json
import logging

def setup_logging():
    """Set up logging configuration."""
    logging.basicConfig(
        filename='json_processing.log',
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def process_json_files(folder_path, output_file):
    """Process JSON files in the specified folder and write results to output file."""
    # Set up logging
    setup_logging()
    logging.info("Starting JSON processing for folder: %s", folder_path)

    # Open the output file in write mode
    with open(output_file, 'w', encoding='utf-8') as outfile:
        # Iterate through files in the specified folder (no subfolders)
        for filename in os.listdir(folder_path):
            if filename.endswith('.json'):  # Process only JSON files
                file_path = os.path.join(folder_path, filename)
                # Check if it's a file (not a directory)
                if os.path.isfile(file_path):
                    try:
                        # Read and parse the JSON file
                        with open(file_path, 'r', encoding='utf-8') as infile:
                            data = json.load(infile)

                        # Ensure the JSON is a dictionary
                        if not isinstance(data, dict):
                            logging.error("File %s: JSON is not a dictionary", filename)
                            continue

                        # Check if 'address' key exists and is a list
                        if 'address' not in data:
                            logging.error("File %s: 'address' key not found", filename)
                            continue

                        address = data['address']
                        if not isinstance(address, list):
                            logging.error("File %s: 'address' is not a list", filename)
                            continue

                        # Ensure the address list has at least 3 items
                        if len(address) < 3:
                            logging.error("File %s: 'address' list has fewer than 3 items", filename)
                            continue

                        # Extract the second and third items from the address list
                        second_item = str(address[1])  # City (e.g., "Santa Fe")
                        third_item = str(address[2])   # State (e.g., "NM")
                        # Combine the two items with a space
                        combined = f"{second_item} {third_item}"
                        # Write to output file
                        outfile.write(combined + '\n')
                        logging.info("File %s: Successfully processed - %s", filename, combined)

                    except json.JSONDecodeError as e:
                        logging.error("File %s: Invalid JSON format - %s", filename, str(e))
                    except Exception as e:
                        logging.error("File %s: Unexpected error - %s", filename, str(e))
                else:
                    logging.warning("File %s: Skipped as it is not a file (possible directory)", filename)
            else:
                logging.debug("File %s: Skipped as it is not a JSON file", filename)

    logging.info("Completed JSON processing. Output written to %s", output_file)

# Example usage
folder_path = 'parsed_results'  # Local folder named parsed_results
output_file = 'output.txt'      # Output file name
process_json_files(folder_path, output_file)
