import os
import json
import logging
import requests
from bs4 import BeautifulSoup
from dotenv import load_dotenv
from markdownify import markdownify as md
from typing import Dict, Any, List
from urllib.parse import urljoin, urlparse, urlunparse
from time import sleep

logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scraper.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class APIError(Exception):
    pass

class ScrapelessScraper:
    def __init__(self):
        load_dotenv()
        self.api_key = os.getenv('SCRAPELESS_API')
        if not self.api_key:
            raise ValueError("API key not found in .env file")

        self.endpoint = "https://api.scrapeless.com/api/v1/unlocker/request"
        self.max_retries = 3
        self.retry_delay = 5
        self.session = requests.Session()

    def _normalize_url(self, url: str) -> List[str]:
        """Normalize URL and return HTTPS and HTTP variants"""
        # Remove any extra slashes and ensure proper format
        url = url.strip('/')
        if not url.startswith(('http://', 'https://')):
            url = f"www.{url}" if not url.startswith('www.') else url

        # Clean the URL
        parsed = urlparse(f"https://{url.removeprefix('https://').removeprefix('http://')}")
        cleaned_url = urlunparse(parsed)

        return [cleaned_url]

    def _make_request(self, url: str, retry_count: int = 0) -> Dict[str, Any]:
        headers = {
            'Content-Type': 'application/json',
            'x-api-token': self.api_key
        }

        payload = {
            "actor": "unlocker.webunlocker",
            "proxy": {"country": "US"},
            "input": {
                "url": url,
                "method": "GET",
                "redirect": True,
                "js_render": True,
                "timeout": 30000,
                "js_instructions": [
                    {"wait": 3000},
                    {"scroll": 0},
                    {"wait": 500},
                    {"scroll": 300},
                    {"wait": 500},
                    {"scroll": 600},
                    {"wait": 500},
                    {"scroll": 0},
                    {"wait": 1000}
                ],
                "block": {"resources": ["image", "stylesheet", "font"]},
                "headers": {
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
                    "Accept-Language": "en-US,en;q=0.9"
                }
            }
        }

        try:
            logger.info(f"Requesting content from: {url} (attempt {retry_count + 1})")
            logger.debug(f"Request payload: {json.dumps(payload, indent=2)}")

            response = self.session.post(self.endpoint, headers=headers, json=payload)
            response.raise_for_status()

            response_data = response.json()

            if response_data.get('code') == 200:
                data = response_data.get('data', '')
                if isinstance(data, dict):
                    html_content = data.get('html', '')
                    if not html_content or len(html_content) < 100:
                        raise APIError("Empty or invalid HTML content received")
                    return response_data
                elif isinstance(data, str):
                    if not data or len(data) < 100 or '301 Moved Permanently' in data or '302 Found' in data:
                        if retry_count < self.max_retries:
                            logger.warning(f"Got invalid response (attempt {retry_count + 1}/{self.max_retries}), retrying...")
                            sleep(self.retry_delay)
                            return self._make_request(url, retry_count + 1)
                        raise APIError("Failed to get valid response after maximum retries")
                    return response_data

            raise APIError(f"API returned non-200 code: {response_data.get('code')}")

        except requests.exceptions.RequestException as e:
            if retry_count < self.max_retries:
                wait_time = self.retry_delay * (2 ** retry_count)
                logger.warning(f"Request failed, waiting {wait_time}s before retry...")
                sleep(wait_time)
                return self._make_request(url, retry_count + 1)
            logger.error(f"API request failed: {str(e)}")
            if hasattr(e, 'response') and e.response is not None:
                logger.error(f"Error response: {e.response.text}")
            raise APIError(f"Failed to fetch content: {str(e)}")

    def _process_images(self, soup: BeautifulSoup, base_url: str) -> None:
        for idx, img in enumerate(soup.find_all('img'), 1):
            src = img.get('src', '')
            if src:
                abs_src = urljoin(base_url, src)
                alt_text = img.get('alt', f'Image {idx}')
                new_tag = soup.new_tag('p')
                new_tag.string = f"![{alt_text}]({abs_src})"
                img.replace_with(new_tag)

    def _clean_html(self, html: str, base_url: str) -> str:
        soup = BeautifulSoup(html, 'html.parser')

        for element in soup.select('script, style, meta, link, iframe'):
            element.decompose()

        self._process_images(soup, base_url)
        return str(soup)

    def _format_markdown(self, md_content: str) -> str:
        lines = md_content.split('\n')
        formatted_lines = []
        in_heading = False

        for line in lines:
            line = line.strip()
            if not line:
                continue

            if line.startswith('#'):
                formatted_lines.extend(['', line, ''])
                in_heading = True
            elif line.startswith('!['):
                formatted_lines.extend(['', line, ''])
            else:
                formatted_lines.append(line)
                in_heading = False

        return '\n'.join(formatted_lines)

    def scrape_and_format(self, url: str) -> str:
        urls_to_try = self._normalize_url(url)
        last_error = None

        for test_url in urls_to_try:
            try:
                logger.info(f"Attempting to scrape: {test_url}")
                response = self._make_request(test_url)

                if not isinstance(response.get('data', ''), str):
                    logger.warning(f"Invalid response format from {test_url}")
                    continue

                html_content = response['data']
                logger.info("Processing HTML content")
                cleaned_html = self._clean_html(html_content, test_url)

                logger.info("Converting to Markdown")
                markdown_content = md(cleaned_html)

                logger.info("Formatting Markdown content")
                formatted_content = self._format_markdown(markdown_content)

                final_content = f"""Title: Milwaukee Vet Clinic – For the love of your animals

URL Source: {test_url}

Markdown Content:
{formatted_content}"""

                return final_content

            except Exception as e:
                logger.error(f"Failed to scrape {test_url}: {str(e)}")
                last_error = e

        raise APIError(f"Failed to scrape content from all URLs. Last error: {str(last_error)}")

def main():
    try:
        scraper = ScrapelessScraper()
        url = "https://mkevet.com/services/"
        logger.info(f"Starting scrape of {url}")

        content = scraper.scrape_and_format(url)

        output_file = "scraped_content.md"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(content)

        logger.info(f"Content successfully scraped and saved to {output_file}")
        return 0

    except Exception as e:
        logger.error(f"Script failed: {str(e)}")
        return 1

if __name__ == "__main__":
    exit(main())
