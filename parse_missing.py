#!/usr/bin/env python3
"""
Script to identify and parse websites that have been crawled but not parsed.
This version uses concurrent processing to speed up parsing while respecting API rate limits.

Gemini API limits:
- 4000 requests per minute
- 50 concurrent sessions
"""

import os
import re
import glob
import json
import argparse
import time
import threading
import concurrent.futures
from urllib.parse import urlparse
from datetime import datetime
from processing_tracker import ProcessingTracker, ProcessingStage, StatusCode

# Import the necessary functions from parser.py
try:
    from parser import GEMINI_API_KEY
    from gemini_parser import GeminiParser
    from gemini_api import clean_json_data
except ImportError:
    print("Warning: Could not import all required modules from parser.py")
    GEMINI_API_KEY = os.environ.get('GEMINI_API_KEY')

# Global counter for API rate limiting
api_calls_counter = 0
api_calls_lock = threading.Lock()
api_calls_reset_time = time.time()

def extract_domain_from_filename(filename):
    """Extract domain from a filename"""
    # Pattern: domain_timestamp_crawled.txt or domain_timestamp.json
    base = os.path.basename(filename)
    if '_' in base:
        parts = base.split('_')
        if len(parts) >= 2:
            return parts[0]  # First part is the domain
    return None

def find_crawled_files(raw_dir="parsed_results/raw"):
    """Find all crawled files in the raw directory"""
    crawled_files = glob.glob(os.path.join(raw_dir, "*_crawled.txt"))
    return crawled_files

def find_parsed_files(output_dir="parsed_results"):
    """Find all parsed JSON files in the output directory and its subdirectories"""
    parsed_files = []

    # Check main directory
    parsed_files.extend(glob.glob(os.path.join(output_dir, "*.json")))

    # Check Dog_cat subdirectory
    dog_cat_dir = os.path.join(output_dir, "Dog_cat")
    if os.path.exists(dog_cat_dir):
        parsed_files.extend(glob.glob(os.path.join(dog_cat_dir, "*.json")))

    # Check pet_store subdirectory
    pet_store_dir = os.path.join(output_dir, "pet_store")
    if os.path.exists(pet_store_dir):
        parsed_files.extend(glob.glob(os.path.join(pet_store_dir, "*.json")))

    return parsed_files

def identify_missing_parses(crawled_files, parsed_files):
    """Identify sites that have been crawled but not parsed"""
    # Extract domains from parsed files
    parsed_domains = set()
    for parsed_file in parsed_files:
        domain = extract_domain_from_filename(parsed_file)
        if domain:
            parsed_domains.add(domain)

    # Find crawled files without corresponding parsed files
    missing_parses = []
    for crawled_file in crawled_files:
        domain = extract_domain_from_filename(crawled_file)
        if domain and domain not in parsed_domains:
            missing_parses.append((domain, crawled_file))

    return missing_parses

def extract_homepage_url(content):
    """Extract the homepage URL from the crawled content"""
    # Look for the starting URL in the content
    url_match = re.search(r'# Starting URL:\s*(.*?)$', content, re.MULTILINE)
    if url_match:
        return url_match.group(1).strip()

    # If not found, try to extract from URL lines
    url_match = re.search(r'URL:\s*(https?://[^\s]+)', content)
    if url_match:
        return url_match.group(1).strip()

    return None

def check_api_rate_limit():
    """Check if we're within API rate limits and wait if necessary"""
    global api_calls_counter, api_calls_reset_time

    with api_calls_lock:
        current_time = time.time()

        # Reset counter every minute
        if current_time - api_calls_reset_time >= 60:
            api_calls_counter = 0
            api_calls_reset_time = current_time

        # If we're approaching the limit, wait until the next minute
        if api_calls_counter >= 3900:  # Leave some buffer
            wait_time = 60 - (current_time - api_calls_reset_time)
            if wait_time > 0:
                print(f"Approaching API rate limit, waiting {wait_time:.2f} seconds...")
                time.sleep(wait_time)
                api_calls_counter = 0
                api_calls_reset_time = time.time()

        # Increment the counter
        api_calls_counter += 1

def parse_content(crawled_file, output_dir="parsed_results", tracker=None):
    """Parse a crawled file using the Gemini API"""
    # Check API rate limit
    check_api_rate_limit()

    # Extract domain and timestamp from filename
    filename = os.path.basename(crawled_file)
    parts = filename.split('_')
    domain = parts[0]
    timestamp = parts[1] if len(parts) > 1 else datetime.now().strftime("%Y%m%d_%H%M%S")

    print(f"Parsing {domain}...")

    # Create raw directory if it doesn't exist
    raw_dir = os.path.join(output_dir, "raw")
    os.makedirs(raw_dir, exist_ok=True)

    # Read the crawled content
    with open(crawled_file, 'r', encoding='utf-8') as f:
        content = f.read()

    # Extract homepage URL
    homepage_url = extract_homepage_url(content)

    # Always update status, even if tracker is None
    if tracker is None:
        # Create a tracker if one wasn't provided
        tracker = ProcessingTracker(
            output_dir="failed_sites",
            parsed_dir=output_dir,
            status_dir="failed_sites/status",
            locks_dir="failed_sites/locks"
        )

    # Update status to indicate parsing has started
    tracker.update_status(
        homepage_url or domain,
        ProcessingStage.PARSING,
        StatusCode.IN_PROGRESS,
        details="Starting Gemini API parsing"
    )

    try:
        # Get the API key
        api_key = os.environ.get('GEMINI_API_KEY', GEMINI_API_KEY)
        if not api_key:
            error_msg = "Gemini API key is required for parsing."
            print(f"Error: {error_msg}")
            if tracker:
                tracker.update_status(
                    homepage_url or domain,
                    ProcessingStage.PARSING,
                    StatusCode.FAILURE,
                    details=error_msg
                )
            return False

        # JSON output file - in the main output directory
        json_output_file = os.path.join(output_dir, f"{domain}_{timestamp}.json")

        # Initialize the parser and parse the content
        parser = GeminiParser(api_key=api_key)

        # Parse the content
        print(f"Sending content to Gemini API for parsing...")

        # Save preprocessed content in the raw directory for debugging
        preprocessed_file = os.path.join(raw_dir, f"{domain}_{timestamp}_preprocessed.txt")

        # Parse with preprocessing
        parsed_data = parser.parse_content(
            content,
            save_preprocessed=True,
            preprocessed_file=preprocessed_file,
            skip_preprocessing=False,
            homepage_url=homepage_url
        )

        if not parsed_data:
            error_msg = "Gemini API returned empty or invalid data"
            print(f"Warning: {error_msg}")
            if tracker:
                tracker.update_status(
                    homepage_url or domain,
                    ProcessingStage.PARSING,
                    StatusCode.FAILURE,
                    details=error_msg
                )
            return False

        # Check if this is partial data from a truncated response
        if parsed_data.get('partial_data'):
            warning_msg = "Using partial data extracted from truncated Gemini API response"
            print(f"Warning: {warning_msg}")
            if tracker:
                tracker.update_status(
                    homepage_url or domain,
                    ProcessingStage.PARSING,
                    StatusCode.PARTIAL,
                    details=warning_msg
                )

        # Clean the JSON data to remove special characters
        cleaned_data = clean_json_data(parsed_data)

        # Save the cleaned parsed data to a JSON file
        with open(json_output_file, 'w', encoding='utf-8') as f:
            json.dump(cleaned_data, f, indent=2, ensure_ascii=False)

        print(f"Parsed data saved to {json_output_file}")

        # Always update status
        tracker.update_status(
            homepage_url or domain,
            ProcessingStage.PARSING,
            StatusCode.SUCCESS if not parsed_data.get('partial_data') else StatusCode.PARTIAL,
            details=f"Parsing completed, saved to {json_output_file}"
        )

        # Update overall status to completed
        tracker.update_status(
            homepage_url or domain,
            ProcessingStage.COMPLETED,
            StatusCode.SUCCESS,
            details=f"All processing stages completed successfully"
        )

        return True

    except Exception as e:
        error_msg = f"Error processing content with Gemini API: {e}"
        print(f"Error: {error_msg}")

        # Always update status
        tracker.update_status(
            homepage_url or domain,
            ProcessingStage.PARSING,
            StatusCode.FAILURE,
            details=error_msg
        )
        return False

def main():
    parser = argparse.ArgumentParser(description='Parse websites that have been crawled but not parsed')
    parser.add_argument('--raw-dir', default='parsed_results/raw', help='Directory containing crawled files')
    parser.add_argument('--output-dir', default='parsed_results', help='Directory to save parsed results')
    parser.add_argument('--parse', action='store_true', help='Parse the missing files (otherwise just list them)')
    parser.add_argument('--max-workers', type=int, default=30, help='Maximum number of concurrent workers (default: 30)')
    parser.add_argument('--status-dir', default='failed_sites/status', help='Directory to store processing status files')
    parser.add_argument('--locks-dir', default='failed_sites/locks', help='Directory to store lock files')
    args = parser.parse_args()

    # Create directories if they don't exist
    os.makedirs(args.raw_dir, exist_ok=True)
    os.makedirs(args.output_dir, exist_ok=True)
    os.makedirs(args.status_dir, exist_ok=True)
    os.makedirs(args.locks_dir, exist_ok=True)

    # Initialize the processing tracker
    tracker = ProcessingTracker(
        output_dir="failed_sites",
        parsed_dir=args.output_dir,
        status_dir=args.status_dir,
        locks_dir=args.locks_dir
    )

    # Find all crawled files
    crawled_files = find_crawled_files(args.raw_dir)
    print(f"Found {len(crawled_files)} crawled files")

    # Find all parsed files
    parsed_files = find_parsed_files(args.output_dir)
    print(f"Found {len(parsed_files)} parsed files")

    # Identify missing parses
    missing_parses = identify_missing_parses(crawled_files, parsed_files)
    print(f"Found {len(missing_parses)} sites that need to be parsed")

    # Print the list of missing parses
    for i, (domain, crawled_file) in enumerate(missing_parses[:10]):
        print(f"{i+1}. {domain} - {crawled_file}")

    if len(missing_parses) > 10:
        print(f"... and {len(missing_parses) - 10} more sites")

    # Parse the missing files if requested
    if args.parse and missing_parses:
        print(f"\nParsing {len(missing_parses)} missing files using up to {args.max_workers} concurrent workers...")

        success_count = 0
        failure_count = 0

        # Use ThreadPoolExecutor for concurrent processing
        with concurrent.futures.ThreadPoolExecutor(max_workers=args.max_workers) as executor:
            # Submit all parsing tasks
            future_to_domain = {
                executor.submit(parse_content, crawled_file, args.output_dir, tracker): domain
                for domain, crawled_file in missing_parses
            }

            # Process results as they complete
            for i, future in enumerate(concurrent.futures.as_completed(future_to_domain)):
                domain = future_to_domain[future]
                try:
                    success = future.result()
                    if success:
                        success_count += 1
                    else:
                        failure_count += 1
                except Exception as e:
                    print(f"Error processing {domain}: {e}")
                    failure_count += 1

                # Print progress
                if (i + 1) % 10 == 0 or (i + 1) == len(missing_parses):
                    print(f"Progress: {i + 1}/{len(missing_parses)} ({(i + 1) / len(missing_parses) * 100:.1f}%)")

        print(f"\nParsing completed: {success_count} succeeded, {failure_count} failed")

        # Generate and save a status report
        status_report_file = tracker.save_status_report()

        # Generate a summary of the processing status
        report = tracker.generate_status_report()
        summary = report["summary"]

        print("\n=== PROCESSING STATUS SUMMARY ===")
        print(f"Total sites processed: {summary['total_sites']}")
        print(f"Completed successfully: {summary['completed_sites']}")
        print(f"Partially completed: {summary['partial_sites']}")
        print(f"Failed: {summary['failed_sites']}")
        print(f"In progress: {summary['in_progress_sites']}")
        print(f"Not started: {summary['not_started_sites']}")
        print(f"Success rate: {summary['success_rate']:.2f}%")
        print(f"\nDetailed status report saved to: {status_report_file}")

if __name__ == "__main__":
    main()
